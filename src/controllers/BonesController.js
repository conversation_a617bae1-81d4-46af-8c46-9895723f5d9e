const ApiController = require('./ApiController');
const BoneService = require('../services/BoneService');
// const BonesIndexInput = require('../inputs/BonesIndexInput');
const BoneOutput = require('../outputs/BoneOutput');

class BonesController extends ApiController {
  constructor() {
    super();
    this.service = new BoneService();
  }

  /**
   * Get all bones (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    // const input = new BonesIndexInput(req.query);
    // input.validate();
    // const result = await this.service.findAll(input.output());
    const result = await this.service.findAll();

    const output = new BoneOutput(result.bones, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new BonesController();
